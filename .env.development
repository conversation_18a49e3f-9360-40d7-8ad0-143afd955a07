# 开发环境配置
NODE_ENV=development

# 应用配置
VUE_APP_TITLE=环贝仪表板 (开发环境)
VUE_APP_VERSION=1.0.0
VUE_APP_DESCRIPTION=Vue2 + ElementUI2 仪表板项目 - 开发环境

# API 配置
VUE_APP_API_URL=http://localhost:3000/api
VUE_APP_API_TIMEOUT=15000
VUE_APP_API_RETRY_COUNT=3

# 基础路径
VUE_APP_BASE_URL=/
VUE_APP_PUBLIC_PATH=/

# 开发服务器配置
VUE_APP_DEV_PORT=8080
VUE_APP_DEV_HOST=localhost

# 功能开关
VUE_APP_ENABLE_MOCK=true
VUE_APP_ENABLE_DEBUG=true
VUE_APP_ENABLE_CONSOLE_LOG=true
VUE_APP_ENABLE_ERROR_LOG=true

# 第三方服务配置
VUE_APP_ENABLE_ANALYTICS=false
VUE_APP_SENTRY_DSN=

# 上传配置
VUE_APP_UPLOAD_URL=http://localhost:3000/upload
VUE_APP_UPLOAD_MAX_SIZE=10485760
VUE_APP_UPLOAD_ALLOWED_TYPES=image/jpeg,image/png,image/gif,application/pdf

# WebSocket 配置
VUE_APP_WS_URL=ws://localhost:3001
VUE_APP_WS_RECONNECT_INTERVAL=5000

# 缓存配置
VUE_APP_CACHE_PREFIX=huanbei_dev_
VUE_APP_CACHE_EXPIRE=3600000

# 主题配置
VUE_APP_DEFAULT_THEME=light
VUE_APP_THEME_COLOR=#409eff

# 地图配置
VUE_APP_MAP_KEY=your_development_map_key
VUE_APP_MAP_TYPE=amap
