<template>
  <div id="app">
    <el-container>
      <el-header>
        <div class="header-content">
          <h1>环贝仪表板</h1>
          <el-menu
            :default-active="$route.path"
            class="el-menu-demo"
            mode="horizontal"
            router
          >
            <el-menu-item index="/">首页</el-menu-item>
            <el-menu-item index="/dashboard">仪表板</el-menu-item>
            <el-menu-item index="/about">关于</el-menu-item>
          </el-menu>
        </div>
      </el-header>
      <el-main>
        <router-view />
      </el-main>
    </el-container>
  </div>
</template>

<script>
export default {
  name: 'App'
}
</script>

<style lang="scss">
// 变量定义
$primary-color: #409eff;
$text-color: #2c3e50;
$white: white;
$header-height: 60px;

#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: $text-color;
}

.el-header {
  background-color: $primary-color;
  color: $white;
  padding: 0;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  padding: 0 20px;

  h1 {
    margin: 0;
    font-size: 24px;
  }
}

.el-menu-demo {
  background-color: transparent;
  border-bottom: none;
}

.el-menu--horizontal {
  > .el-menu-item {
    color: $white;
    border-bottom: 2px solid transparent;

    &:hover,
    &.is-active {
      background-color: rgba($white, 0.1);
      border-bottom-color: $white;
    }
  }
}

.el-main {
  padding: 20px;
  min-height: calc(100vh - #{$header-height});
}
</style>
