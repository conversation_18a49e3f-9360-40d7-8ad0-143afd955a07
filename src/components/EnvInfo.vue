<template>
  <el-card class="env-info-card">
    <div slot="header" class="clearfix">
      <span>环境变量信息</span>
      <el-tag :type="envType" style="float: right;">
        {{ currentEnv }}
      </el-tag>
    </div>
    
    <div class="env-content">
      <el-row :gutter="20">
        <el-col :span="12">
          <div class="info-section">
            <h4>应用信息</h4>
            <div class="info-item">
              <span class="label">应用标题:</span>
              <span class="value">{{ appTitle }}</span>
            </div>
            <div class="info-item">
              <span class="label">版本:</span>
              <span class="value">{{ appVersion }}</span>
            </div>
            <div class="info-item">
              <span class="label">描述:</span>
              <span class="value">{{ appDescription }}</span>
            </div>
          </div>
        </el-col>
        
        <el-col :span="12">
          <div class="info-section">
            <h4>API 配置</h4>
            <div class="info-item">
              <span class="label">API 地址:</span>
              <span class="value">{{ apiUrl }}</span>
            </div>
            <div class="info-item">
              <span class="label">超时时间:</span>
              <span class="value">{{ apiTimeout }}ms</span>
            </div>
            <div class="info-item">
              <span class="label">重试次数:</span>
              <span class="value">{{ apiRetryCount }}</span>
            </div>
          </div>
        </el-col>
      </el-row>
      
      <el-row :gutter="20" style="margin-top: 20px;">
        <el-col :span="12">
          <div class="info-section">
            <h4>功能开关</h4>
            <div class="info-item">
              <span class="label">Mock 数据:</span>
              <el-tag :type="enableMock ? 'success' : 'info'" size="mini">
                {{ enableMock ? '启用' : '禁用' }}
              </el-tag>
            </div>
            <div class="info-item">
              <span class="label">调试模式:</span>
              <el-tag :type="enableDebug ? 'warning' : 'info'" size="mini">
                {{ enableDebug ? '启用' : '禁用' }}
              </el-tag>
            </div>
            <div class="info-item">
              <span class="label">控制台日志:</span>
              <el-tag :type="enableConsoleLog ? 'success' : 'info'" size="mini">
                {{ enableConsoleLog ? '启用' : '禁用' }}
              </el-tag>
            </div>
          </div>
        </el-col>
        
        <el-col :span="12">
          <div class="info-section">
            <h4>上传配置</h4>
            <div class="info-item">
              <span class="label">上传地址:</span>
              <span class="value">{{ uploadUrl }}</span>
            </div>
            <div class="info-item">
              <span class="label">最大大小:</span>
              <span class="value">{{ formatFileSize(uploadMaxSize) }}</span>
            </div>
            <div class="info-item">
              <span class="label">允许类型:</span>
              <span class="value">{{ uploadAllowedTypes }}</span>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
  </el-card>
</template>

<script>
export default {
  name: 'EnvInfo',
  computed: {
    currentEnv() {
      return process.env.NODE_ENV || 'development'
    },
    envType() {
      const env = this.currentEnv
      return env === 'production' ? 'success' : env === 'development' ? 'warning' : 'info'
    },
    appTitle() {
      return process.env.VUE_APP_TITLE || '环贝仪表板'
    },
    appVersion() {
      return process.env.VUE_APP_VERSION || '1.0.0'
    },
    appDescription() {
      return process.env.VUE_APP_DESCRIPTION || 'Vue2 + ElementUI2 仪表板项目'
    },
    apiUrl() {
      return process.env.VUE_APP_API_URL || '/api'
    },
    apiTimeout() {
      return process.env.VUE_APP_API_TIMEOUT || '10000'
    },
    apiRetryCount() {
      return process.env.VUE_APP_API_RETRY_COUNT || '2'
    },
    enableMock() {
      return process.env.VUE_APP_ENABLE_MOCK === 'true'
    },
    enableDebug() {
      return process.env.VUE_APP_ENABLE_DEBUG === 'true'
    },
    enableConsoleLog() {
      return process.env.VUE_APP_ENABLE_CONSOLE_LOG === 'true'
    },
    uploadUrl() {
      return process.env.VUE_APP_UPLOAD_URL || '/api/upload'
    },
    uploadMaxSize() {
      return parseInt(process.env.VUE_APP_UPLOAD_MAX_SIZE) || 5242880
    },
    uploadAllowedTypes() {
      return process.env.VUE_APP_UPLOAD_ALLOWED_TYPES || 'image/jpeg,image/png'
    }
  },
  methods: {
    formatFileSize(bytes) {
      if (bytes === 0) return '0 Bytes'
      const k = 1024
      const sizes = ['Bytes', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    }
  }
}
</script>

<style lang="scss" scoped>
.env-info-card {
  margin: 20px 0;
}

.env-content {
  .info-section {
    h4 {
      margin-bottom: 15px;
      color: #409eff;
      border-bottom: 1px solid #ebeef5;
      padding-bottom: 8px;
    }
  }
  
  .info-item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    
    .label {
      font-weight: 600;
      min-width: 100px;
      color: #606266;
    }
    
    .value {
      color: #303133;
      word-break: break-all;
    }
  }
}
</style>
