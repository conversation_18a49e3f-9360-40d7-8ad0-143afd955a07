<template>
  <div class="dashboard">
    <el-row :gutter="20">
      <el-col :span="6">
        <stat-card
          :value="dashboardData.totalUsers"
          label="总用户数"
          icon="el-icon-user"
          icon-class="user-icon"
        />
      </el-col>

      <el-col :span="6">
        <stat-card
          :value="dashboardData.totalOrders"
          label="总订单数"
          icon="el-icon-shopping-cart-2"
          icon-class="order-icon"
        />
      </el-col>

      <el-col :span="6">
        <stat-card
          :value="dashboardData.totalRevenue"
          label="总收入"
          icon="el-icon-money"
          icon-class="revenue-icon"
          format="currency"
        />
      </el-col>

      <el-col :span="6">
        <stat-card
          :value="dashboardData.growthRate"
          label="增长率"
          icon="el-icon-trend-charts"
          icon-class="growth-icon"
          format="percent"
        />
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px">
      <el-col :span="12">
        <el-card>
          <div slot="header">
            <span>最近活动</span>
            <el-button
              style="float: right; padding: 3px 0"
              type="text"
              @click="refreshData"
            >
              刷新数据
            </el-button>
          </div>
          <el-timeline>
            <el-timeline-item
              v-for="(activity, index) in activities"
              :key="index"
              :timestamp="activity.timestamp"
              :type="activity.type"
            >
              {{ activity.content }}
            </el-timeline-item>
          </el-timeline>
        </el-card>
      </el-col>

      <el-col :span="12">
        <el-card>
          <div slot="header">
            <span>快速操作</span>
          </div>
          <div class="quick-operations">
            <el-button-group>
              <el-button type="primary" icon="el-icon-plus">新增用户</el-button>
              <el-button type="success" icon="el-icon-edit">编辑数据</el-button>
              <el-button type="warning" icon="el-icon-download"
                >导出报告</el-button
              >
            </el-button-group>

            <div style="margin-top: 20px">
              <el-progress :percentage="75" status="success"></el-progress>
              <p style="margin-top: 10px; color: #666">系统性能: 良好</p>
            </div>

            <div style="margin-top: 20px">
              <el-tag type="success">在线</el-tag>
              <el-tag type="info" style="margin-left: 10px"
                >{{ onlineUsers }} 用户在线</el-tag
              >
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex'
import StatCard from '@/components/StatCard.vue'

export default {
  name: 'Dashboard',
  components: {
    StatCard
  },
  data() {
    return {
      onlineUsers: 156,
      activities: [
        {
          content: '用户张三完成了订单支付',
          timestamp: '2024-01-15 10:30',
          type: 'success'
        },
        {
          content: '系统自动备份完成',
          timestamp: '2024-01-15 09:15',
          type: 'primary'
        },
        {
          content: '新用户李四注册成功',
          timestamp: '2024-01-15 08:45',
          type: 'success'
        },
        {
          content: '数据库优化任务执行',
          timestamp: '2024-01-15 07:30',
          type: 'warning'
        }
      ]
    }
  },
  computed: {
    ...mapGetters(['getDashboardData']),
    dashboardData() {
      return this.getDashboardData
    }
  },
  methods: {
    ...mapActions(['fetchDashboardData']),
    refreshData() {
      this.$message({
        message: '正在刷新数据...',
        type: 'info'
      })
      this.fetchDashboardData()
      setTimeout(() => {
        this.$message({
          message: '数据刷新成功！',
          type: 'success'
        })
      }, 1000)
    }
  }
}
</script>

<style lang="scss" scoped>
// 变量定义
$icon-size: 60px;
$transition-duration: 0.3s;
$white: white;
$text-color: #2c3e50;
$label-color: #7f8c8d;
$button-min-width: 120px;

// 渐变色定义
$user-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
$order-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
$revenue-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
$growth-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);

.stat-card {
  cursor: pointer;
  transition: all $transition-duration;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}

.stat-content {
  display: flex;
  align-items: center;
  padding: 10px;
}

.stat-icon {
  width: $icon-size;
  height: $icon-size;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 24px;
  color: $white;
}

.user-icon {
  background: $user-gradient;
}

.order-icon {
  background: $order-gradient;
}

.revenue-icon {
  background: $revenue-gradient;
}

.growth-icon {
  background: $growth-gradient;
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 28px;
  font-weight: bold;
  color: $text-color;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: $label-color;
}

.quick-operations {
  padding: 10px 0;
}

.el-button-group {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;

  .el-button {
    flex: 1;
    min-width: $button-min-width;
  }
}
</style>
