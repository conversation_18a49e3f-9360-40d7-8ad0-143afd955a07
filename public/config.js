// 环境配置
window.CONFIG = {
  // 开发环境配置
  development: {
    baseURL: 'http://localhost:3000/api',
    timeout: 15000,
    // 其他开发环境配置
    debug: true,
    mock: false
  },
  
  // 生产环境配置
  production: {
    baseURL: '/api',
    timeout: 15000,
    // 其他生产环境配置
    debug: false,
    mock: false
  },
  
  // 测试环境配置
  test: {
    baseURL: 'http://test-api.example.com/api',
    timeout: 15000,
    debug: true,
    mock: false
  }
}

// 获取当前环境配置
window.getConfig = function() {
  // 从 HTML 的 data-env 属性获取环境，或者通过其他方式判断
  const env = document.documentElement.getAttribute('data-env') ||
              (window.location.hostname === 'localhost' ? 'development' : 'production')
  return window.CONFIG[env] || window.CONFIG.development
}